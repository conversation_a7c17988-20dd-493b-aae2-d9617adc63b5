"""
清理管理器
负责临时文件清理、日志压缩备份、内存缓存清理等
"""

import os
import gzip
import shutil
import logging
import asyncio
import time
import glob
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
import gc

logger = logging.getLogger(__name__)


class CleanupManager:
    """清理管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化清理管理器
        
        Args:
            config: 清理配置
        """
        self.config = config
        
        # 清理配置
        self.temp_dir = config.get('temp_dir', 'temp')
        self.log_dir = config.get('log_dir', 'logs')
        self.cleanup_interval = config.get('cleanup_interval', 3600)  # 1小时
        
        # 文件清理配置
        self.temp_file_max_age = config.get('temp_file_max_age', 3600)  # 1小时
        self.log_file_max_age = config.get('log_file_max_age', 7 * 24 * 3600)  # 7天
        self.max_log_files = config.get('max_log_files', 10)
        
        # 内存清理配置
        self.memory_cleanup_interval = config.get('memory_cleanup_interval', 1800)  # 30分钟
        self.gc_threshold = config.get('gc_threshold', 100)  # MB
        
        # 运行状态
        self.is_running = False
        self.cleanup_task = None
        self.memory_cleanup_task = None
        
        # 统计信息
        self.cleanup_stats = {
            'temp_files_cleaned': 0,
            'log_files_compressed': 0,
            'log_files_deleted': 0,
            'bytes_freed': 0,
            'last_cleanup_time': 0,
            'last_memory_cleanup_time': 0
        }
        
    async def start(self):
        """启动清理管理器"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 确保目录存在
        self._ensure_directories()
        
        # 启动清理任务
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.memory_cleanup_task = asyncio.create_task(self._memory_cleanup_loop())
        
        logger.info("Cleanup manager started")
        
    async def stop(self):
        """停止清理管理器"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 停止清理任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
                
        if self.memory_cleanup_task:
            self.memory_cleanup_task.cancel()
            try:
                await self.memory_cleanup_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Cleanup manager stopped")
        
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.temp_dir, self.log_dir]:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
    async def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                await self._run_cleanup()
                await asyncio.sleep(self.cleanup_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(self.cleanup_interval)
                
    async def _memory_cleanup_loop(self):
        """内存清理循环"""
        while self.is_running:
            try:
                await self._run_memory_cleanup()
                await asyncio.sleep(self.memory_cleanup_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Memory cleanup loop error: {e}")
                await asyncio.sleep(self.memory_cleanup_interval)
                
    async def _run_cleanup(self):
        """执行清理操作"""
        start_time = time.time()
        
        try:
            # 清理临时文件
            temp_cleaned = await self._cleanup_temp_files()
            
            # 压缩和清理日志文件
            log_compressed, log_deleted = await self._cleanup_log_files()
            
            # 更新统计信息
            self.cleanup_stats['temp_files_cleaned'] += temp_cleaned
            self.cleanup_stats['log_files_compressed'] += log_compressed
            self.cleanup_stats['log_files_deleted'] += log_deleted
            self.cleanup_stats['last_cleanup_time'] = time.time()
            
            duration = time.time() - start_time
            logger.info(f"Cleanup completed in {duration:.2f}s: "
                       f"temp={temp_cleaned}, log_compressed={log_compressed}, log_deleted={log_deleted}")
                       
        except Exception as e:
            logger.error(f"Cleanup operation failed: {e}")
            
    async def _cleanup_temp_files(self) -> int:
        """清理临时文件"""
        if not os.path.exists(self.temp_dir):
            return 0
            
        cleaned_count = 0
        current_time = time.time()
        
        try:
            for root, dirs, files in os.walk(self.temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    try:
                        # 检查文件年龄
                        file_age = current_time - os.path.getmtime(file_path)
                        
                        if file_age > self.temp_file_max_age:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            
                            cleaned_count += 1
                            self.cleanup_stats['bytes_freed'] += file_size
                            
                            logger.debug(f"Removed temp file: {file_path}")
                            
                    except Exception as e:
                        logger.warning(f"Failed to remove temp file {file_path}: {e}")
                        
                # 清理空目录
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        if not os.listdir(dir_path):  # 空目录
                            os.rmdir(dir_path)
                            logger.debug(f"Removed empty directory: {dir_path}")
                    except Exception as e:
                        logger.debug(f"Failed to remove directory {dir_path}: {e}")
                        
        except Exception as e:
            logger.error(f"Temp file cleanup error: {e}")
            
        return cleaned_count
        
    async def _cleanup_log_files(self) -> tuple[int, int]:
        """清理和压缩日志文件"""
        if not os.path.exists(self.log_dir):
            return 0, 0
            
        compressed_count = 0
        deleted_count = 0
        current_time = time.time()
        
        try:
            # 获取所有日志文件
            log_files = []
            for pattern in ['*.log', '*.log.*']:
                log_files.extend(glob.glob(os.path.join(self.log_dir, pattern)))
                
            # 按修改时间排序
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            for i, log_file in enumerate(log_files):
                try:
                    file_age = current_time - os.path.getmtime(log_file)
                    
                    # 跳过当前正在使用的日志文件
                    if log_file.endswith('.log') and i == 0:
                        continue
                        
                    # 压缩旧日志文件
                    if (not log_file.endswith('.gz') and 
                        file_age > 24 * 3600 and  # 1天后压缩
                        file_age < self.log_file_max_age):
                        
                        await self._compress_log_file(log_file)
                        compressed_count += 1
                        
                    # 删除过期日志文件
                    elif file_age > self.log_file_max_age or i >= self.max_log_files:
                        file_size = os.path.getsize(log_file)
                        os.remove(log_file)
                        
                        deleted_count += 1
                        self.cleanup_stats['bytes_freed'] += file_size
                        
                        logger.debug(f"Removed old log file: {log_file}")
                        
                except Exception as e:
                    logger.warning(f"Failed to process log file {log_file}: {e}")
                    
        except Exception as e:
            logger.error(f"Log file cleanup error: {e}")
            
        return compressed_count, deleted_count
        
    async def _compress_log_file(self, log_file: str):
        """压缩日志文件"""
        try:
            compressed_file = log_file + '.gz'
            
            # 在线程池中执行压缩操作
            await asyncio.get_event_loop().run_in_executor(
                None, self._compress_file, log_file, compressed_file
            )
            
            # 删除原文件
            os.remove(log_file)
            
            logger.debug(f"Compressed log file: {log_file} -> {compressed_file}")
            
        except Exception as e:
            logger.error(f"Failed to compress log file {log_file}: {e}")
            
    def _compress_file(self, source_file: str, target_file: str):
        """压缩文件（在线程池中执行）"""
        with open(source_file, 'rb') as f_in:
            with gzip.open(target_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
                
    async def _run_memory_cleanup(self):
        """执行内存清理"""
        try:
            # 获取当前内存使用情况
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            # 如果内存使用超过阈值，执行垃圾回收
            if memory_mb > self.gc_threshold:
                # 强制垃圾回收
                collected = gc.collect()
                
                # 获取回收后的内存使用情况
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                freed_mb = memory_mb - new_memory_mb
                
                self.cleanup_stats['last_memory_cleanup_time'] = time.time()
                
                logger.info(f"Memory cleanup: collected {collected} objects, "
                           f"freed {freed_mb:.1f}MB (from {memory_mb:.1f}MB to {new_memory_mb:.1f}MB)")
            else:
                logger.debug(f"Memory usage OK: {memory_mb:.1f}MB (threshold: {self.gc_threshold}MB)")
                
        except Exception as e:
            logger.error(f"Memory cleanup error: {e}")
            
    def create_temp_file(self, prefix: str = "asr_", suffix: str = ".tmp") -> str:
        """
        创建临时文件
        
        Args:
            prefix: 文件名前缀
            suffix: 文件名后缀
            
        Returns:
            临时文件路径
        """
        import tempfile
        
        # 确保临时目录存在
        Path(self.temp_dir).mkdir(parents=True, exist_ok=True)
        
        # 创建临时文件
        fd, temp_path = tempfile.mkstemp(
            prefix=prefix,
            suffix=suffix,
            dir=self.temp_dir
        )
        
        # 关闭文件描述符
        os.close(fd)
        
        logger.debug(f"Created temp file: {temp_path}")
        return temp_path
        
    def cleanup_temp_file(self, file_path: str):
        """立即清理指定的临时文件"""
        try:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                os.remove(file_path)
                self.cleanup_stats['bytes_freed'] += file_size
                logger.debug(f"Cleaned up temp file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
            
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取清理统计信息"""
        return {
            **self.cleanup_stats,
            'temp_dir_size_mb': self._get_directory_size(self.temp_dir) / 1024 / 1024,
            'log_dir_size_mb': self._get_directory_size(self.log_dir) / 1024 / 1024,
            'is_running': self.is_running
        }
        
    def _get_directory_size(self, directory: str) -> int:
        """获取目录大小（字节）"""
        try:
            total_size = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        pass
            return total_size
        except Exception:
            return 0
            
    async def force_cleanup(self):
        """强制执行一次清理"""
        logger.info("Force cleanup requested")
        await self._run_cleanup()
        await self._run_memory_cleanup()
