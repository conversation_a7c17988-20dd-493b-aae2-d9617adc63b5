"""
Enhanced Stream ASR Server
增强流式语音识别服务器主程序
"""

import asyncio
import logging
import signal
import sys
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

from core.session import SessionManager
from api.websocket import WebSocketHandler
from api.http import HTTPEndpoints
from utils.config import ConfigManager
from utils.logger import setup_logger, configure_logging_from_config
from utils.monitoring import SystemMonitor, HealthMonitor, CleanupManager

# 全局变量
session_manager: SessionManager = None
websocket_handler: WebSocketHandler = None
config_manager: ConfigManager = None
system_monitor: SystemMonitor = None
health_monitor: HealthMonitor = None
cleanup_manager: CleanupManager = None
logger = None


# 健康检查函数
def _check_system_resources():
    """检查系统资源"""
    try:
        if system_monitor:
            current_metrics = system_monitor.get_current_metrics()
            if current_metrics:
                # 检查CPU使用率
                if current_metrics.cpu_percent > 90:
                    return "critical", f"High CPU usage: {current_metrics.cpu_percent:.1f}%"
                elif current_metrics.cpu_percent > 80:
                    return "warning", f"Elevated CPU usage: {current_metrics.cpu_percent:.1f}%"

                # 检查内存使用率
                if current_metrics.memory_percent > 90:
                    return "critical", f"High memory usage: {current_metrics.memory_percent:.1f}%"
                elif current_metrics.memory_percent > 80:
                    return "warning", f"Elevated memory usage: {current_metrics.memory_percent:.1f}%"

                # 检查磁盘使用率
                if current_metrics.disk_percent > 95:
                    return "critical", f"High disk usage: {current_metrics.disk_percent:.1f}%"
                elif current_metrics.disk_percent > 85:
                    return "warning", f"Elevated disk usage: {current_metrics.disk_percent:.1f}%"

                return "healthy", "System resources OK"
            else:
                return "warning", "No system metrics available"
        else:
            return "unknown", "System monitor not available"
    except Exception as e:
        return "critical", f"System check failed: {e}"


def _check_session_manager():
    """检查会话管理器"""
    try:
        if session_manager:
            stats = session_manager.get_statistics()
            session_count = stats.get("total_sessions", 0)

            # 检查会话数量
            max_sessions = config_manager.get("websocket.max_connections", 100) if config_manager else 100
            if session_count > max_sessions * 0.9:
                return "warning", f"High session count: {session_count}/{max_sessions}"

            return "healthy", f"Session manager OK ({session_count} sessions)"
        else:
            return "critical", "Session manager not available"
    except Exception as e:
        return "critical", f"Session manager check failed: {e}"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global session_manager, websocket_handler, config_manager, system_monitor, health_monitor, cleanup_manager, logger

    try:
        # 初始化配置管理器
        config_manager = ConfigManager("configs")

        # 配置日志
        logger = configure_logging_from_config(config_manager.server_config)
        logger.info("Enhanced Stream ASR Server starting...")

        # 初始化系统监控
        system_monitor = SystemMonitor(
            collect_interval=config_manager.get("monitoring.collect_interval", 30.0)
        )
        await system_monitor.start()

        # 初始化健康监控
        health_monitor = HealthMonitor(
            check_interval=config_manager.get("monitoring.check_interval", 30.0)
        )

        # 注册健康检查项
        health_monitor.register_check(
            "system_resources",
            _check_system_resources,
            interval=60.0,
            critical=True
        )

        await health_monitor.start()

        # 初始化清理管理器
        cleanup_config = {
            'temp_dir': config_manager.get("cleanup.temp_dir", "temp"),
            'log_dir': config_manager.get("cleanup.log_dir", "logs"),
            'cleanup_interval': config_manager.get("cleanup.cleanup_interval", 3600),
            'temp_file_max_age': config_manager.get("cleanup.temp_file_max_age", 3600),
            'log_file_max_age': config_manager.get("cleanup.log_file_max_age", 7 * 24 * 3600)
        }
        cleanup_manager = CleanupManager(cleanup_config)
        await cleanup_manager.start()

        # 初始化会话管理器
        session_manager = SessionManager(config_manager)

        # 初始化WebSocket处理器
        websocket_handler = WebSocketHandler(session_manager, config_manager)

        # 注册会话管理器健康检查
        health_monitor.register_check(
            "session_manager",
            _check_session_manager,
            interval=30.0,
            critical=True
        )

        logger.info("Server initialization completed")

        yield

    except Exception as e:
        if logger:
            logger.error(f"Server initialization failed: {e}")
        else:
            print(f"Server initialization failed: {e}")
        raise
    finally:
        # 清理资源
        if logger:
            logger.info("Server shutting down...")

        if cleanup_manager:
            await cleanup_manager.stop()

        if health_monitor:
            await health_monitor.stop()

        if system_monitor:
            await system_monitor.stop()

        if session_manager:
            await session_manager.shutdown()

        if logger:
            logger.info("Server shutdown completed")


# 创建FastAPI应用
app = FastAPI(
    title="Enhanced Stream ASR",
    description="增强流式语音识别服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.websocket("/ws/stream")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket流式识别端点"""
    client_id = str(uuid.uuid4())
    
    try:
        await websocket_handler.handle_connection(websocket, client_id)
    except WebSocketDisconnect:
        logger.info(f"WebSocket client disconnected: {client_id}")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")


@app.get("/")
async def root():
    """根路径，返回Web界面"""
    try:
        with open("web/static/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Enhanced Stream ASR</title></head>
                <body>
                    <h1>Enhanced Stream ASR Server</h1>
                    <p>Web界面文件未找到。请确保web/static/index.html文件存在。</p>
                    <p>WebSocket端点: /ws/stream</p>
                    <p>API文档: <a href="/docs">/docs</a></p>
                </body>
            </html>
            """,
            status_code=200
        )


@app.get("/health")
async def health_check():
    """增强健康检查端点"""
    try:
        # 获取健康监控状态
        health_status = {}
        if health_monitor:
            health_status = health_monitor.get_health_status()
        else:
            health_status = {
                "overall_status": "unknown",
                "components": {},
                "message": "Health monitor not available"
            }

        # 添加系统指标
        if system_monitor:
            current_metrics = system_monitor.get_current_metrics()
            if current_metrics:
                health_status["system_metrics"] = {
                    "cpu_percent": current_metrics.cpu_percent,
                    "memory_percent": current_metrics.memory_percent,
                    "disk_percent": current_metrics.disk_percent,
                    "process_count": current_metrics.process_count
                }

        # 添加会话统计
        if session_manager:
            health_status["session_statistics"] = session_manager.get_statistics()

        # 添加清理统计
        if cleanup_manager:
            health_status["cleanup_statistics"] = cleanup_manager.get_cleanup_stats()

        return health_status

    except Exception as e:
        if logger:
            logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.get("/api/languages")
async def get_supported_languages():
    """获取支持的语种列表"""
    try:
        languages = config_manager.get_supported_languages()
        return {
            "languages": languages,
            "default": config_manager.get("asr.default_language", "zh")
        }
    except Exception as e:
        logger.error(f"Failed to get supported languages: {e}")
        raise HTTPException(status_code=500, detail="Failed to get supported languages")


@app.get("/api/config")
async def get_server_config():
    """获取服务器配置信息"""
    try:
        return {
            "audio": {
                "sample_rate": config_manager.get("audio.sample_rate", 16000),
                "channels": config_manager.get("audio.channels", 1),
                "chunk_duration": config_manager.get("audio.chunk_duration", 0.4),
                "supported_sample_rates": config_manager.get("audio.supported_sample_rates", [16000])
            },
            "asr": {
                "supported_languages": config_manager.get_supported_languages(),
                "enable_auto_language_detection": config_manager.get("asr.enable_auto_language_detection", True),
                "enable_intermediate_result": config_manager.get("asr.enable_intermediate_result", True),
                "enable_punctuation": config_manager.get("asr.enable_punctuation", True)
            },
            "websocket": {
                "heartbeat_interval": config_manager.get("websocket.heartbeat_interval", 30),
                "max_connections": config_manager.get("websocket.max_connections", 100)
            }
        }
    except Exception as e:
        logger.error(f"Failed to get server config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get server config")


@app.get("/api/statistics")
async def get_statistics():
    """获取服务器统计信息"""
    try:
        stats = {}

        if session_manager:
            stats["sessions"] = session_manager.get_statistics()

        if websocket_handler:
            stats["connections"] = websocket_handler.get_connection_stats()

        if system_monitor:
            stats["system"] = {
                "current_metrics": system_monitor.get_current_metrics().__dict__ if system_monitor.get_current_metrics() else {},
                "average_metrics": system_monitor.get_average_metrics(60),
                "system_info": system_monitor.get_system_info()
            }

        if cleanup_manager:
            stats["cleanup"] = cleanup_manager.get_cleanup_stats()

        return stats

    except Exception as e:
        if logger:
            logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")


@app.get("/api/monitoring/system")
async def get_system_metrics():
    """获取系统监控指标"""
    try:
        if not system_monitor:
            raise HTTPException(status_code=503, detail="System monitor not available")

        current_metrics = system_monitor.get_current_metrics()
        average_metrics = system_monitor.get_average_metrics(60)
        system_info = system_monitor.get_system_info()

        return {
            "current": current_metrics.__dict__ if current_metrics else {},
            "average_60min": average_metrics,
            "system_info": system_info
        }

    except HTTPException:
        raise
    except Exception as e:
        if logger:
            logger.error(f"Failed to get system metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system metrics")


@app.get("/api/monitoring/health")
async def get_health_details():
    """获取详细健康状态"""
    try:
        if not health_monitor:
            raise HTTPException(status_code=503, detail="Health monitor not available")

        return health_monitor.get_health_status()

    except HTTPException:
        raise
    except Exception as e:
        if logger:
            logger.error(f"Failed to get health details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get health details")


@app.post("/api/monitoring/cleanup")
async def force_cleanup():
    """强制执行清理操作"""
    try:
        if not cleanup_manager:
            raise HTTPException(status_code=503, detail="Cleanup manager not available")

        await cleanup_manager.force_cleanup()

        return {
            "status": "success",
            "message": "Cleanup completed",
            "statistics": cleanup_manager.get_cleanup_stats()
        }

    except HTTPException:
        raise
    except Exception as e:
        if logger:
            logger.error(f"Failed to force cleanup: {e}")
        raise HTTPException(status_code=500, detail="Failed to force cleanup")


# 挂载静态文件
try:
    app.mount("/static", StaticFiles(directory="web/static"), name="static")
except Exception as e:
    print(f"Warning: Failed to mount static files: {e}")


def signal_handler(signum, frame):
    """信号处理器"""
    if logger:
        logger.info(f"Received signal {signum}, shutting down...")
    else:
        print(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 临时配置管理器用于获取服务器配置
        temp_config = ConfigManager("configs")
        
        # 获取服务器配置
        host = temp_config.get("server.host", "0.0.0.0")
        port = temp_config.get("server.port", 8080)
        workers = temp_config.get("server.workers", 1)
        debug = temp_config.get("server.debug", False)
        
        print(f"Starting Enhanced Stream ASR Server on {host}:{port}")
        
        # 启动服务器
        uvicorn.run(
            "server:app",
            host=host,
            port=port,
            workers=workers if not debug else 1,
            reload=debug,
            log_level="info"
        )
        
    except Exception as e:
        print(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
